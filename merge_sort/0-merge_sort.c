#include <stdlib.h>
#include <stdio.h>
#include "sort.h"

void merge(int *array, int *temp, size_t left, size_t mid, size_t right);
void merge_sort_recursive(int *array, int *temp, size_t left, size_t right);

/**
 * merge_sort - Sorts an array of integers in ascending order using Merge Sort
 * @array: Array to sort
 * @size: Number of elements in the array
 */
void merge_sort(int *array, size_t size)
{
    int *temp;

    if (!array || size <= 1)
        return;

    /* Allocate temporary array once */
    temp = malloc(sizeof(int) * size);
    if (!temp)
        return;

    merge_sort_recursive(array, temp, 0, size);

    /* Free temporary array */
    free(temp);
}

/**
 * merge_sort_recursive - Recursive helper function for merge sort
 * @array: Array to sort
 * @temp: Temporary array for merging
 * @left: Starting index of the sub-array
 * @right: Ending index of the sub-array (exclusive)
 */
void merge_sort_recursive(int *array, int *temp, size_t left, size_t right)
{
    if (right - left <= 1)
        return;

    /* Calculate mid point, ensuring left sub-array is <= right sub-array */
    size_t mid = left + (right - left) / 2;

    /* Sort left sub-array */
    merge_sort_recursive(array, temp, left, mid);
    /* Sort right sub-array */
    merge_sort_recursive(array, temp, mid, right);
    /* Merge the two sorted sub-arrays */
    merge(array, temp, left, mid, right);
}

/**
 * merge - Merges two sorted sub-arrays into a single sorted array
 * @array: Original array
 * @temp: Temporary array for merging
 * @left: Starting index of the left sub-array
 * @mid: Ending index of the left sub-array (exclusive)
 * @right: Ending index of the right sub-array (exclusive)
 */
void merge(int *array, int *temp, size_t left, size_t mid, size_t right)
{
    size_t i = left, j = mid, k = 0;

    /* Print left sub-array */
    printf("Merging...\n[left]: ");
    print_array(array + left, mid - left);
    /* Print right sub-array */
    printf("[right]: ");
    print_array(array + mid, right - mid);

    /* Merge the two sub-arrays into temp */
    while (i < mid && j < right)
    {
        if (array[i] <= array[j])
            temp[k++] = array[i++];
        else
            temp[k++] = array[j++];
    }

    /* Copy remaining elements from left sub-array, if any */
    while (i < mid)
        temp[k++] = array[i++];

    /* Copy remaining elements from right sub-array, if any */
    while (j < right)
        temp[k++] = array[j++];

    /* Copy merged elements back to original array */
    for (i = 0; i < k; i++)
        array[left + i] = temp[i];

    /* Print merged result */
    printf("[Done]: ");
    print_array(array + left, right - left);
}