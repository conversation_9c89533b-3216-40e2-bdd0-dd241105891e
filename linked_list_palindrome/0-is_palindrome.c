#include "lists.h"
#include <stddef.h>

/**
 * reverse_list - reverses a singly linked list
 * @head: pointer to the head of the list
 *
 * Return: pointer to the new head of the reversed list
 */
listint_t *reverse_list(listint_t *head)
{
	listint_t *prev = NULL;
	listint_t *current = head;
	listint_t *next = NULL;

	while (current != NULL)
	{
		next = current->next;
		current->next = prev;
		prev = current;
		current = next;
	}

	return (prev);
}

/**
 * is_palindrome - checks if a singly linked list is a palindrome
 * @head: pointer to pointer to the head of the list
 *
 * Return: 0 if it is not a palindrome, 1 if it is a palindrome
 */
int is_palindrome(listint_t **head)
{
	listint_t *slow, *fast, *prev_slow, *second_half, *middle_node;
	int result = 1;

	/* Empty list is considered a palindrome */
	if (head == NULL || *head == NULL)
		return (1);

	/* Single node is a palindrome */
	if ((*head)->next == NULL)
		return (1);

	/* Find the middle using slow and fast pointers */
	slow = *head;
	fast = *head;
	prev_slow = NULL;

	while (fast != NULL && fast->next != NULL)
	{
		fast = fast->next->next;
		prev_slow = slow;
		slow = slow->next;
	}

	/* If fast is not NULL, then length is odd and slow is middle */
	middle_node = NULL;
	if (fast != NULL)
	{
		middle_node = slow;
		slow = slow->next;
	}

	/* Split the list and reverse the second half */
	second_half = slow;
	if (prev_slow != NULL)
		prev_slow->next = NULL;
	second_half = reverse_list(second_half);

	/* Compare the first half and reversed second half */
	result = 1;
	listint_t *first_half = *head;
	listint_t *temp_second = second_half;

	while (first_half != NULL && temp_second != NULL)
	{
		if (first_half->n != temp_second->n)
		{
			result = 0;
			break;
		}
		first_half = first_half->next;
		temp_second = temp_second->next;
	}

	/* Restore the original list */
	second_half = reverse_list(second_half);
	if (middle_node != NULL)
	{
		if (prev_slow != NULL)
			prev_slow->next = middle_node;
		middle_node->next = second_half;
	}
	else
	{
		if (prev_slow != NULL)
			prev_slow->next = second_half;
	}

	return (result);
}
