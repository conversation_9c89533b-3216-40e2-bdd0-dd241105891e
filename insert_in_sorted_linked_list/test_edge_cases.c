#include <stdlib.h>
#include <stdio.h>
#include "lists.h"

/**
 * main - Test edge cases for insert_node function
 *
 * Return: Always 0.
 */
int main(void)
{
    listint_t *head;
    listint_t *result;

    printf("=== Test 1: Insert into empty list ===\n");
    head = NULL;
    result = insert_node(&head, 42);
    if (result)
        printf("Successfully inserted %d\n", result->n);
    print_listint(head);
    free_listint(head);

    printf("\n=== Test 2: Insert at beginning ===\n");
    head = NULL;
    add_nodeint_end(&head, 5);
    add_nodeint_end(&head, 10);
    add_nodeint_end(&head, 15);
    printf("Original list:\n");
    print_listint(head);
    printf("After inserting 1:\n");
    insert_node(&head, 1);
    print_listint(head);
    free_listint(head);

    printf("\n=== Test 3: Insert at end ===\n");
    head = NULL;
    add_nodeint_end(&head, 5);
    add_nodeint_end(&head, 10);
    add_nodeint_end(&head, 15);
    printf("Original list:\n");
    print_listint(head);
    printf("After inserting 20:\n");
    insert_node(&head, 20);
    print_listint(head);
    free_listint(head);

    printf("\n=== Test 4: Insert duplicate ===\n");
    head = NULL;
    add_nodeint_end(&head, 5);
    add_nodeint_end(&head, 10);
    add_nodeint_end(&head, 15);
    printf("Original list:\n");
    print_listint(head);
    printf("After inserting 10 (duplicate):\n");
    insert_node(&head, 10);
    print_listint(head);
    free_listint(head);

    return (0);
}
