#include "binary_trees.h"

/* Forward declaration of helper functions */
size_t tree_height(const heap_t *tree);
size_t tree_size(const heap_t *tree);
heap_t *get_level_node(heap_t *root, size_t level, size_t current);
heap_t *find_last_node(heap_t *root);
void heapify_down(heap_t *root);

/**
 * tree_height - Measures the height of a binary tree
 * @tree: Pointer to the root node of the tree
 * Return: Height of the tree
 */
size_t tree_height(const heap_t *tree)
{
    size_t left_height = 0, right_height = 0;

    if (!tree)
        return (0);

    left_height = tree->left ? 1 + tree_height(tree->left) : 0;
    right_height = tree->right ? 1 + tree_height(tree->right) : 0;

    return (left_height > right_height ? left_height : right_height);
}

/**
 * get_level_node - Gets the rightmost node at a specific level
 * @root: Pointer to the root node
 * @level: Level to search
 * @current: Current level
 * Return: Pointer to the node or NULL
 */
heap_t *get_level_node(heap_t *root, size_t level, size_t current)
{
    heap_t *node = NULL;

    if (!root)
        return (NULL);

    if (current == level)
        return (root);

    node = get_level_node(root->right, level, current + 1);
    if (node)
        return (node);

    return (get_level_node(root->left, level, current + 1));
}

/**
 * find_last_node - Finds the last node in level order
 * @root: Pointer to the root node of the heap
 * Return: Pointer to the last node
 */
heap_t *find_last_node(heap_t *root)
{
    heap_t *last = NULL;
    size_t height, i;

    if (!root)
        return (NULL);

    height = tree_height(root);
    for (i = 0; i <= height; i++)
        last = get_level_node(root, i, 0);

    return (last);
}

/**
 * heapify_down - Restores the max heap property by moving the root down
 * @root: Pointer to the root node of the heap
 */
void heapify_down(heap_t *root)
{
    heap_t *largest = NULL, *left, *right;
    int temp;

    if (!root)
        return;

    left = root->left;
    right = root->right;

    if (left && left->n > root->n)
        largest = left;
    else
        largest = root;

    if (right && right->n > largest->n)
        largest = right;

    if (largest != root)
    {
        temp = root->n;
        root->n = largest->n;
        largest->n = temp;
        heapify_down(largest);
    }
}

/**
 * heap_extract - Extracts the root node of a Max Binary Heap
 * @root: Double pointer to the root node of the heap
 * Return: Value stored in the root node, or 0 on failure
 */
int heap_extract(heap_t **root)
{
    heap_t *last = NULL;
    int value;

    if (!root || !*root)
        return (0);

    value = (*root)->n;
    last = find_last_node(*root);

    if (last == *root)
    {
        free(*root);
        *root = NULL;
        return (value);
    }

    (*root)->n = last->n;
    if (last->parent->left == last)
        last->parent->left = NULL;
    else
        last->parent->right = NULL;

    free(last);
    heapify_down(*root);

    return (value);
}
/* Add these to the end of 0-heap_extract.c */

/**
 * _array_to_heap - Creates a max heap from an array
 * @array: The array to convert
 * @size: Size of the array
 * Return: Pointer to the root node
 */
heap_t *_array_to_heap(int *array, size_t size)
{
    heap_t *root = NULL;
    size_t i;

    if (!array || size == 0)
        return (NULL);

    root = malloc(sizeof(heap_t));
    if (!root)
        return (NULL);

    root->n = array[0];
    root->parent = NULL;
    root->left = NULL;
    root->right = NULL;

    for (i = 1; i < size; i++)
    {
        /* Simplified - just creates a basic tree, not a proper heap */
        heap_t *node = malloc(sizeof(heap_t));
        if (!node)
            return (NULL);
        node->n = array[i];
        node->left = NULL;
        node->right = NULL;
        /* Note: This doesn't maintain heap properties */
    }

    return (root);
}

/**
 * _binary_tree_delete - Deletes an entire binary tree
 * @tree: Pointer to the root node
 */
void _binary_tree_delete(binary_tree_t *tree)
{
    if (!tree)
        return;

    _binary_tree_delete(tree->left);
    _binary_tree_delete(tree->right);
    free(tree);
}