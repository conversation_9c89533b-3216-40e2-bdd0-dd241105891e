#!/usr/bin/python3
"""
Module to generate <PERSON>'s Triangle up to n rows.
"""

def pascal_triangle(n):
    """
    Generates <PERSON>'s Triangle up to the nth row.

    Args:
        n (int): The number of rows to generate. Must be an integer.

    Returns:
        list[list[int]]: A list of lists containing integers representing
        <PERSON>'s Triangle. Each inner list represents a row in the triangle.
        Returns an empty list if n <= 0.

    Examples:
        >>> pascal_triangle(5)
        [[1], [1, 1], [1, 2, 1], [1, 3, 3, 1], [1, 4, 6, 4, 1]]
        >>> pascal_triangle(1)
        [[1]]
        >>> pascal_triangle(0)
        []
    """
def pascal_triangle(n):
    if n <= 0:
        return []
    
    triangle = [[1]]
    
    for i in range(1, n):
        prev_row = triangle[-1]
        new_row = [1]  # First element is always 1
        
        # Calculate middle elements
        for j in range(len(prev_row) - 1):
            new_row.append(prev_row[j] + prev_row[j + 1])
            
        new_row.append(1)  # Last element is always 1
        triangle.append(new_row)
        
    return triangle