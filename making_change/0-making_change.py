#!/usr/bin/python3
"""Script that determines the fewest number of coins needed to meet a given total."""

def makeChange(coins, total):
    """
    Determines the fewest number of coins needed to meet a given total.

    Args:
        coins (list): List of coin denominations (positive integers).
        total (int): Target amount to make.

    Returns:
        int: Fewest number of coins needed, or -1 if total cannot be met.
    """
    if total <= 0:
        return 0
    
    if not coins:
        return -1
    
    # Sort coins in descending order
    coins.sort(reverse=True)
    
    remaining = total
    coin_count = 0
    
    for coin in coins:
        if remaining == 0:
            break
        if coin <= remaining:
            # Use as many of this coin as possible
            count = remaining // coin
            coin_count += count
            remaining -= count * coin
    
    # If remaining is not 0, total cannot be met
    return coin_count if remaining == 0 else -1