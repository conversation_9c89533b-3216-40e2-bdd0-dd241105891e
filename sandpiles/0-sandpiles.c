#include "sandpiles.h"

/**
 * print_grid - Print 3x3 grid
 * @grid: 3x3 grid
 */
static void print_grid(int grid[3][3])
{
	int i, j;

	for (i = 0; i < 3; i++)
	{
		for (j = 0; j < 3; j++)
		{
			if (j)
				printf(" ");
			printf("%d", grid[i][j]);
		}
		printf("\n");
	}
}

/**
 * is_stable - Check if a sandpile is stable
 * @grid: 3x3 grid to check
 *
 * Return: 1 if stable, 0 if unstable
 */
static int is_stable(int grid[3][3])
{
	int i, j;

	for (i = 0; i < 3; i++)
	{
		for (j = 0; j < 3; j++)
		{
			if (grid[i][j] > 3)
				return (0);
		}
	}
	return (1);
}

/**
 * topple_sandpile - Perform one round of toppling
 * @grid: 3x3 grid to topple
 */
static void topple_sandpile(int grid[3][3])
{
	int i, j;
	int temp_grid[3][3];

	/* Copy the grid to temp_grid */
	for (i = 0; i < 3; i++)
	{
		for (j = 0; j < 3; j++)
		{
			temp_grid[i][j] = grid[i][j];
		}
	}

	/* Process each cell that needs toppling */
	for (i = 0; i < 3; i++)
	{
		for (j = 0; j < 3; j++)
		{
			if (temp_grid[i][j] > 3)
			{
				/* Remove 4 grains from current cell */
				grid[i][j] -= 4;

				/* Distribute 1 grain to each neighbor */
				/* Up */
				if (i > 0)
					grid[i - 1][j] += 1;
				/* Down */
				if (i < 2)
					grid[i + 1][j] += 1;
				/* Left */
				if (j > 0)
					grid[i][j - 1] += 1;
				/* Right */
				if (j < 2)
					grid[i][j + 1] += 1;
			}
		}
	}
}

/**
 * sandpiles_sum - Computes the sum of two sandpiles
 * @grid1: Left 3x3 grid (result will be stored here)
 * @grid2: Right 3x3 grid
 */
void sandpiles_sum(int grid1[3][3], int grid2[3][3])
{
	int i, j;

	/* Add grid2 to grid1 */
	for (i = 0; i < 3; i++)
	{
		for (j = 0; j < 3; j++)
		{
			grid1[i][j] += grid2[i][j];
		}
	}

	/* Keep toppling until stable */
	while (!is_stable(grid1))
	{
		printf("=\n");
		print_grid(grid1);
		topple_sandpile(grid1);
	}
}
