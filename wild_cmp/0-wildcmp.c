#include "holberton.h"

/**
 * wildcmp - compares two strings with wildcard support
 * @s1: first string
 * @s2: second string (can contain * wildcards)
 *
 * Return: 1 if strings can be considered identical, 0 otherwise
 */
int wildcmp(char *s1, char *s2)
{
    /* If both strings are empty, they match */
    if (*s1 == '\0' && *s2 == '\0')
        return (1);
    
    /* If pattern is empty but string isn't, no match */
    if (*s2 == '\0')
        return (0);
    
    /* If current character in pattern is '*' */
    if (*s2 == '*')
    {
        /* Skip consecutive '*' characters */
        while (*s2 == '*')
            s2++;
        
        /* If pattern ends with '*', it matches everything */
        if (*s2 == '\0')
            return (1);
        
        /* Try matching '*' with empty string or with characters from s1 */
        while (*s1 != '\0')
        {
            if (wildcmp(s1, s2))
                return (1);
            s1++;
        }
        
        /* Try matching '*' with empty string */
        return (wildcmp(s1, s2));
    }
    
    /* If string is empty but pattern isn't (and pattern isn't '*'), no match */
    if (*s1 == '\0')
        return (0);
    
    /* If current characters match, continue with rest of strings */
    if (*s1 == *s2)
        return (wildcmp(s1 + 1, s2 + 1));
    
    /* Characters don't match */
    return (0);
}
