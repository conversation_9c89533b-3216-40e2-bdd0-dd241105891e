#!/usr/bin/python3
"""
Additional tests for island_perimeter function
"""
island_perimeter = __import__('0-island_perimeter').island_perimeter

if __name__ == "__main__":
    # Test case 1: Single cell island
    grid1 = [[1]]
    print(f"Single cell: {island_perimeter(grid1)}")  # Expected: 4
    
    # Test case 2: No island (all water)
    grid2 = [
        [0, 0, 0],
        [0, 0, 0],
        [0, 0, 0]
    ]
    print(f"No island: {island_perimeter(grid2)}")  # Expected: 0
    
    # Test case 3: 2x2 square island
    grid3 = [
        [0, 0, 0, 0],
        [0, 1, 1, 0],
        [0, 1, 1, 0],
        [0, 0, 0, 0]
    ]
    print(f"2x2 square: {island_perimeter(grid3)}")  # Expected: 8
    
    # Test case 4: Horizontal line
    grid4 = [
        [0, 0, 0, 0, 0],
        [1, 1, 1, 1, 1],
        [0, 0, 0, 0, 0]
    ]
    print(f"Horizontal line: {island_perimeter(grid4)}")  # Expected: 12
    
    # Test case 5: Original test case
    grid5 = [
        [0, 0, 0, 0, 0, 0],
        [0, 1, 0, 0, 0, 0],
        [0, 1, 0, 0, 0, 0],
        [0, 1, 1, 1, 0, 0],
        [0, 0, 0, 0, 0, 0]
    ]
    print(f"Original test: {island_perimeter(grid5)}")  # Expected: 12
