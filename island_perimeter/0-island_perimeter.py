#!/usr/bin/python3
"""
Module for calculating island perimeter
"""


def island_perimeter(grid):
    """
    Returns the perimeter of the island described in grid.
    
    Args:
        grid: List of list of integers where 0 represents water and 1 represents land
        
    Returns:
        Integer representing the perimeter of the island
    """
    if not grid or not grid[0]:
        return 0
    
    rows = len(grid)
    cols = len(grid[0])
    perimeter = 0
    
    for i in range(rows):
        for j in range(cols):
            if grid[i][j] == 1:  # If current cell is land
                # Check all 4 directions (up, down, left, right)
                # Add 1 to perimeter for each side that's exposed to water or boundary
                
                # Check up
                if i == 0 or grid[i-1][j] == 0:
                    perimeter += 1
                
                # Check down
                if i == rows - 1 or grid[i+1][j] == 0:
                    perimeter += 1
                
                # Check left
                if j == 0 or grid[i][j-1] == 0:
                    perimeter += 1
                
                # Check right
                if j == cols - 1 or grid[i][j+1] == 0:
                    perimeter += 1
    
    return perimeter
