#include "lists.h"

/**
 * find_listint_loop - Finds the loop in a singly linked list using <PERSON>'s algorithm
 * @head: Pointer to the head of the list
 *
 * Return: Address of the node where the loop starts, or NULL if no loop
 */
listint_t *find_listint_loop(listint_t *head)
{
    listint_t *slow, *fast;

    if (!head || !head->next)
        return (NULL);

    slow = head;
    fast = head;

    /* Step 1: Detect if a loop exists */
    while (fast && fast->next)
    {
        slow = slow->next;          /* Move one step */
        fast = fast->next->next;    /* Move two steps */
        if (slow == fast)           /* Loop detected */
            break;
    }

    /* If no loop, return NULL */
    if (!fast || !fast->next)
        return (NULL);

    /* Step 2: Find the start of the loop */
    slow = head;
    while (slow != fast)
    {
        slow = slow->next;          /* Move one step */
        fast = fast->next;          /* Move one step */
    }

    return (slow); /* The node where slow and fast meet is the loop start */
}